const {FlatCompat} = require("@eslint/eslintrc");
const js = require("@eslint/js");
const {configs: tsConfigs} = require("@typescript-eslint/eslint-plugin");
const tsParser = require("@typescript-eslint/parser");
const importPlugin = require("eslint-plugin-import");

const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
});

module.exports = [
  {
    files: ["**/*.js"],
    ...js.configs.recommended,
  },
  ...compat.extends("google"),
  {
    files: ["**/*.ts"],
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        ecmaVersion: 2018,
        sourceType: "module",
      },
    },
    plugins: {
      "@typescript-eslint": tsConfigs,
      "import": importPlugin,
    },
    rules: {
      "no-restricted-globals": ["error", "name", "length"],
      "prefer-arrow-callback": "error",
      "quotes": ["error", "double", {"allowTemplateLiterals": true}],
      "import/no-unresolved": 0,
      "indent": ["error", 2],
      "max-len": ["error", {"code": 120}],
    },
  },
  {
    ignores: ["lib/**/*"],
  },
];
