import * as functions from "firebase-functions/v1";
import * as admin from "firebase-admin";
import cors from "cors";
import express from "express";

// Initialize Firebase Admin with service account
try {
  const serviceAccount = require("../config/service-account-key.json");
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: "document-management-c5a96",
    storageBucket: "document-management-c5a96.firebasestorage.app",
  });
  console.log("✅ Firebase Admin initialized with service account key and storage bucket");
} catch (error) {
  console.log("⚠️ Service account key not found, using default credentials");
  admin.initializeApp({
    projectId: "document-management-c5a96",
    storageBucket: "document-management-c5a96.firebasestorage.app",
  });
}

// Initialize Express app with CORS
const app = express();
app.use(cors({origin: true}));

// Import active function modules
import {hybridProcessFileUpload} from "./modules/hybridFileProcessing";
import {getFileAccessUrl} from "./modules/fileAccess";
import {createCategory, updateCategory, deleteCategory} from "./modules/categoryManagement";
import {deleteDocument} from "./modules/documentManagement";
import {createUser} from "./modules/userManagement";
import {logActivity, validateUserSession} from "./modules/authOperations";
import {getActivityStatistics} from "./modules/activityAnalytics";

// ✅ ACTIVE FUNCTIONS ONLY (11 total)

// Core File Processing
export {hybridProcessFileUpload};

// File Access
export {getFileAccessUrl};

// Category Management (Admin Functions)
export {createCategory, updateCategory, deleteCategory};

// Document Management
export {deleteDocument};

// User Management (Admin Functions)
export {createUser};

// Security & Authentication
export {logActivity, validateUserSession};

// Analytics
export {getActivityStatistics};

// Health Check Function
export const healthCheck = functions.https.onCall(async (data, context) => {
  return {
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "2.0.0",
    authenticated: !!context.auth,
    activeFunctions: 11,
  };
});

// API Gateway Function (if needed for future expansion)
export const api = functions.https.onRequest(app);
