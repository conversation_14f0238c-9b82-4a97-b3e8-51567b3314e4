{"name": "managementdoc-functions", "version": "2.0.0-cleaned", "description": "Optimized Cloud Functions for Firebase - Document Management System (11 Active Functions Only)", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest", "postinstall": "npm rebuild sharp --platform=linux --arch=x64"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"cors": "^2.8.5", "express": "^4.19.2", "firebase-admin": "^12.5.0", "firebase-functions": "^6.0.1", "lodash": "^4.17.21", "sharp": "^0.34.2", "uuid": "^10.0.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/lodash": "^4.17.7", "@types/node": "^20.16.10", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0", "eslint": "^9.11.1", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.30.0", "firebase-functions-test": "^3.3.0", "typescript": "^5.6.2"}, "private": true}