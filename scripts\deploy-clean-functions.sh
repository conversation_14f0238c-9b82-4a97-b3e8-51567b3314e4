#!/bin/bash

echo "🚀 DEPLOYING CLEANED FIREBASE FUNCTIONS"
echo "======================================="
echo ""

echo "📋 Functions to be REMOVED (paused/disabled):"
echo "  ❌ addFilesToCategory (disabled)"
echo "  ❌ removeFilesFromCategory (disabled)"
echo "  ❌ getCategoryDocumentsEnhanced (disabled)"
echo "  ❌ refreshCategoryContents (disabled)"
echo "  ❌ invalidateStatisticsCache (disabled)"
echo "  ❌ generateThumbnail (deprecated)"
echo "  ❌ validateFile (redundant)"
echo "  ❌ extractMetadata (redundant)"
echo "  ❌ checkDuplicateFile (redundant)"
echo "  ❌ processFileUpload (old version)"
echo ""

echo "✅ Functions to KEEP:"
echo "  ✅ hybridProcessFileUpload (main function)"
echo "  ✅ streamingUpload (different purpose)"
echo "  ✅ getFileAccessUrl (utility function)"
echo "  ✅ createCategory, updateCategory, deleteCategory"
echo "  ✅ deleteDocument, bulkDocumentOperations"
echo "  ✅ healthCheck, logActivity"
echo "  ✅ All sync and user management functions"
echo ""

read -p "Continue with deployment and cleanup? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Operation cancelled"
    exit 1
fi

echo "🔄 Step 1: Backup current functions list..."
firebase functions:list > functions-backup-$(date +%Y%m%d-%H%M%S).txt

echo "🔄 Step 2: Building functions..."
cd functions
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed! Please check the TypeScript compilation."
    exit 1
fi

echo "✅ Build successful"
echo ""

echo "🔄 Step 3: Deploying updated functions..."
cd ..
firebase deploy --only functions

if [ $? -ne 0 ]; then
    echo "❌ Deployment failed! Please check the logs."
    exit 1
fi

echo "✅ Functions deployed successfully"
echo ""

echo "🔄 Step 4: Removing disabled/paused functions..."

echo "  Removing addFilesToCategory..."
firebase functions:delete addFilesToCategory --force

echo "  Removing removeFilesFromCategory..."
firebase functions:delete removeFilesFromCategory --force

echo "  Removing getCategoryDocumentsEnhanced..."
firebase functions:delete getCategoryDocumentsEnhanced --force

echo "  Removing refreshCategoryContents..."
firebase functions:delete refreshCategoryContents --force

echo "  Removing invalidateStatisticsCache..."
firebase functions:delete invalidateStatisticsCache --force

echo "  Removing generateThumbnail..."
firebase functions:delete generateThumbnail --force

echo "  Removing validateFile..."
firebase functions:delete validateFile --force

echo "  Removing extractMetadata..."
firebase functions:delete extractMetadata --force

echo "  Removing checkDuplicateFile..."
firebase functions:delete checkDuplicateFile --force

echo "  Removing old processFileUpload..."
firebase functions:delete processFileUpload --force

echo ""
echo "🔄 Step 5: Verifying remaining functions..."
firebase functions:list

echo ""
echo "✅ DEPLOYMENT COMPLETED SUCCESSFULLY!"
echo ""
echo "📊 Summary:"
echo "  ✅ Deployed cleaned functions"
echo "  ❌ Removed 10 disabled/paused functions"
echo "  🔧 Optimized for direct client calls"
echo ""
echo "🔍 Next Steps:"
echo "  1. Test core functionality (upload, category management)"
echo "  2. Monitor Firebase console for any errors"
echo "  3. Verify client-side operations work correctly"
echo "  4. Check activity logging is working"
echo ""
