@echo off
echo ========================================
echo 🚀 FIREBASE CLEAN DEPLOYMENT SCRIPT
echo ========================================
echo.
echo 📋 Script ini akan:
echo   1. Install ulang Firebase CLI
echo   2. Install dependencies functions
echo   3. Build dan deploy functions yang sudah dibersihkan
echo   4. Verify deployment
echo.

pause

echo.
echo 🔄 Step 1: Install Firebase CLI...
npm install -g firebase-tools
if %errorlevel% neq 0 (
    echo ❌ Error installing Firebase CLI
    pause
    exit /b 1
)

echo.
echo 🔄 Step 2: Login ke Firebase...
firebase login
if %errorlevel% neq 0 (
    echo ❌ Error logging in to Firebase
    pause
    exit /b 1
)

echo.
echo 🔄 Step 3: Set Firebase project...
firebase use document-management-c5a96
if %errorlevel% neq 0 (
    echo ❌ Error setting Firebase project
    pause
    exit /b 1
)

echo.
echo 🔄 Step 4: Install functions dependencies...
cd functions
npm install
if %errorlevel% neq 0 (
    echo ❌ Error installing functions dependencies
    pause
    exit /b 1
)

echo.
echo 🔄 Step 5: Build functions...
npm run build
if %errorlevel% neq 0 (
    echo ❌ Error building functions
    pause
    exit /b 1
)

echo.
echo 🔄 Step 6: Deploy functions...
cd ..
firebase deploy --only functions
if %errorlevel% neq 0 (
    echo ❌ Error deploying functions
    pause
    exit /b 1
)

echo.
echo 🔄 Step 7: Verify deployment...
firebase functions:list

echo.
echo ✅ DEPLOYMENT COMPLETED SUCCESSFULLY!
echo.
echo 📊 Expected active functions (11 total):
echo   ✅ hybridProcessFileUpload
echo   ✅ getFileAccessUrl
echo   ✅ createCategory
echo   ✅ updateCategory
echo   ✅ deleteCategory
echo   ✅ deleteDocument
echo   ✅ createUser
echo   ✅ logActivity
echo   ✅ validateUserSession
echo   ✅ getActivityStatistics
echo   ✅ healthCheck
echo.

pause
